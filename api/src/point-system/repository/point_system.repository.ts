import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import {
  PointRule,
  pointRulesSchema,
  pointsConfigSchema,
  pointsLogSchema,
} from '@/db/schema/points_system';
import { or, ilike, eq, asc, and, count, desc, sql, gte } from 'drizzle-orm';
import { student_profiles } from '@/db/schema/student_profile';
import {
  PointConfigDto,
  PointRuleDto,
  PointRulesQueryParamsDto,
  pointRulesSortKeys,
  PointsConfigQueryParamsDto,
  pointsConfigSortKeys,
  PointsLogsParams,
  pointsLogsSortKeys,
} from '../dto';
import { PointConstant } from '@app/shared/constants/points-system.constant';
import { PeriodEnumType } from '@/mcq/leader-board/leader-board.types';
import { CACHE_PREFIXES } from '@app/shared/constants/cache.constant';
import { CacheService } from '@app/shared/cache/cache.service';
import { RedisService } from '@app/shared/redis/redis.service';

@Injectable()
export class PointSystemRepository {
  private readonly logger = new Logger(PointSystemRepository.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
    private readonly redisService: RedisService,
    @InjectQueue('general') private readonly generalQueue: Queue,
  ) {}
  /**
   *
   * @param data
   * @param tx
   * @returns
   */
  async createPointConfiguration(
    data: PointConfigDto,
    tx = this.drizzle.db,
  ): Promise<any> {
    try {
      const result = await tx
        .insert(pointsConfigSchema)
        .values(data)
        .returning()
        .execute();
      return result;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  /**
   *
   * @param id
   * @param tx
   * @returns
   */
  async getPointsConfigById(id: string, tx = this.drizzle.db): Promise<any> {
    try {
      const result = await tx
        .select()
        .from(pointsConfigSchema)
        .where(eq(pointsConfigSchema.id, id))
        .execute();
      if (!result.length) {
        throw new NotFoundException('Point configuration not found');
      }
      return result;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  async getAllPointConfiguration(
    query: PointsConfigQueryParamsDto,
    tx = this.drizzle.db,
  ): Promise<{ data: any[]; total: number }> {
    const {
      sort,
      points_name,
      points_value,
      search,
      page = 1,
      limit = 10,
      order = 'asc',
    } = query;

    const offset = (page - 1) * limit;
    const filters: any[] = [];

    try {
      if (search) {
        const searchTerm = `%${search}%`;
        filters.push(
          or(
            ilike(pointsConfigSchema.point_name, searchTerm),
            ilike(pointsConfigSchema.description, searchTerm),
          ),
        );
      }

      if (points_name) {
        const pointsNameTerm = `%${points_name}%`;
        filters.push(ilike(pointsConfigSchema.point_name, pointsNameTerm));
      }

      if (points_value) {
        filters.push(eq(pointsConfigSchema.point_value, points_value));
      }

      const sortField =
        pointsConfigSortKeys[sort!] || pointsConfigSchema.created_at;
      const sortDirection = order === 'asc' ? asc(sortField) : desc(sortField);

      const [points, totalCount] = await Promise.all([
        tx
          .select()
          .from(pointsConfigSchema)
          .where(and(...filters))
          .orderBy(sortDirection)
          .limit(limit)
          .offset(offset),
        tx
          .select({ count: count() })
          .from(pointsConfigSchema)
          .where(and(...filters)),
      ]);
      if (!points.length) {
        throw new NotFoundException('No point configuration found');
      }
      return {
        data: points,
        total: totalCount[0]?.count || 0,
      };
    } catch (error) {
      throw error;
    }
  }

  async updatePointConfiguration(
    id: string,
    query: PointConfigDto,
    tx = this.drizzle.db,
  ): Promise<any> {
    try {
      const result = await tx
        .update(pointsConfigSchema)
        .set(query)
        .where(eq(pointsConfigSchema.id, id))
        .returning()
        .execute();
      return result;
    } catch (error) {}
  }
  /**
   *
   * @param id
   * @param tx
   * @returns
   */
  async removePointConfiguration(
    id: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string }> {
    try {
      const result = await tx
        .delete(pointsConfigSchema)
        .where(eq(pointsConfigSchema.id, id))
        .execute();

      if (result.rowCount === 0) {
        throw new NotFoundException('Point configuration not found');
      }

      return { message: 'Points deleted successfully' };
    } catch (error) {
      throw error;
    }
  }

  /**
   *Reward Part
   * @param data
   * @param tx
   * @returns
   */
  async rewardPoints(
    data: PointsLogsParams,
    tx = this.drizzle.db,
  ): Promise<any> {
    try {
      const result = await tx
        .insert(pointsLogSchema)
        .values(data)
        .returning()
        .execute();
      return result;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  async getAllPointLogs(
    query: PointsConfigQueryParamsDto,
    tx = this.drizzle.db,
  ): Promise<{ data: any[]; total: number }> {
    const {
      institutionId,
      sort,
      points_name,
      points_value,
      search,
      page = 1,
      limit = 10,
      order = 'asc',
    } = query;

    const offset = (page - 1) * limit;
    const filters: any[] = [];

    try {
      // Add search filter
      if (search) {
        const searchTerm = `%${search}%`;
        filters.push(
          or(
            ilike(pointsLogSchema.description, searchTerm),
            ilike(student_profiles.first_name, searchTerm),
            ilike(student_profiles.last_name, searchTerm),
            ilike(student_profiles.other_name, searchTerm),
            ilike(pointsConfigSchema.point_name, searchTerm),
            ilike(pointRulesSchema.module, searchTerm),
            ilike(pointRulesSchema.action, searchTerm),
            ilike(pointRulesSchema.frequency, searchTerm),
          ),
        );
      }

      // Add specific filters
      if (points_name) {
        const pointsNameTerm = `%${points_name}%`;
        filters.push(ilike(pointsConfigSchema.point_name, pointsNameTerm));
      }

      if (points_value) {
        filters.push(eq(pointsLogSchema.points, points_value));
      }

      if (institutionId) {
        filters.push(eq(student_profiles.institution_id, institutionId));
      }
      const sortField = pointsLogsSortKeys[sort!] || pointsLogSchema.created_at;
      const sortDirection = order === 'asc' ? asc(sortField) : desc(sortField);

      // Execute queries in parallel
      const [points, totalCount] = await Promise.all([
        tx
          .select({
            id: pointsLogSchema.id,
            point_name: pointsConfigSchema.point_name,
            points: pointsLogSchema.points,
            description: pointsLogSchema.description,
            created_at: pointsLogSchema.created_at,
            updated_at: pointsLogSchema.updated_at,
            studentId: student_profiles.id,
            studentFirstName: student_profiles.first_name,
            studentLastName: student_profiles.last_name,
            studentOtherName: student_profiles.other_name,
            studentProgramme: student_profiles.programme,
            studentDegree: student_profiles.degree,
            level: sql`${student_profiles.graduation_date} - ${
              student_profiles.enrollment_date
            }`.as('level'),
          })
          .from(pointsLogSchema)
          .leftJoin(
            student_profiles,
            eq(pointsLogSchema.student_id, student_profiles.id),
          )
          .leftJoin(
            pointsConfigSchema,
            eq(pointsLogSchema.point_rule_id, pointsConfigSchema.id),
          )
          .where(and(...filters))
          .orderBy(sortDirection)
          .limit(limit)
          .offset(offset),
        tx
          .select({ count: count() })
          .from(pointsLogSchema)
          .leftJoin(
            student_profiles,
            eq(pointsLogSchema.student_id, student_profiles.id),
          )
          .leftJoin(
            pointsConfigSchema,
            eq(pointsLogSchema.point_rule_id, pointsConfigSchema.id),
          )
          .where(and(...filters)),
      ]);

      return {
        data: points,
        total: totalCount[0]?.count || 0,
      };
    } catch (error) {
      throw error;
    }
  }

  async removePoints(
    pointId: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string }> {
    try {
      const result = await tx
        .delete(pointsLogSchema)
        .where(eq(pointsLogSchema.id, pointId))
        .execute();
      if (result.rowCount === 0) {
        throw new NotFoundException('Point Logs not found');
      }
      return { message: 'Point logs remove successfully' };
    } catch (error) {
      throw error;
    }
  }
  // Point Rules Methods
  async createPointRule(
    data: PointRuleDto,
    tx = this.drizzle.db,
  ): Promise<any> {
    try {
      const result = await tx
        .insert(pointRulesSchema)
        .values(data)
        .returning()
        .execute();
      return result[0];
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  async getPointRule(
    module: string,
    action: string,
    tx = this.drizzle.db,
  ): Promise<any> {
    try {
      const result = await tx
        .select()
        .from(pointRulesSchema)
        .where(
          and(
            eq(pointRulesSchema.module, module),
            eq(pointRulesSchema.action, action),
          ),
        )
        .execute();
      if (!result.length) {
        this.logger.warn('Point rule not found');
        return null;
      }
      return result[0];
    } catch (error) {
      throw error;
    }
  }
  async getAllPointRules(
    query: PointRulesQueryParamsDto,
    tx = this.drizzle.db,
  ): Promise<{ data: PointRule[]; total: number }> {
    const {
      sort,
      module,
      action,
      search,
      page = 1,
      limit = 10,
      order = 'asc',
    } = query;

    const offset = (page - 1) * limit;
    const filters: any[] = [];

    try {
      if (search) {
        const searchTerm = `%${search}%`;
        filters.push(
          or(
            ilike(pointRulesSchema.module, searchTerm),
            ilike(pointRulesSchema.action, searchTerm),
          ),
        );
      }

      if (module) {
        const moduleTerm = `%${module}%`;
        filters.push(ilike(pointRulesSchema.module, moduleTerm));
      }

      if (action) {
        const actionTerm = `%${action}%`;
        filters.push(ilike(pointRulesSchema.action, actionTerm));
      }

      const sortField = sort
        ? pointRulesSortKeys[sort]
        : pointRulesSchema.created_at;
      const sortDirection = order === 'asc' ? asc(sortField) : desc(sortField);

      const [rules, totalCount] = await Promise.all([
        tx
          .select()
          .from(pointRulesSchema)
          .where(and(...filters))
          .orderBy(sortDirection)
          .limit(limit)
          .offset(offset),
        tx
          .select({ count: count() })
          .from(pointRulesSchema)
          .where(and(...filters)),
      ]);

      if (!rules.length) {
        throw new NotFoundException('No point rules found');
      }

      return {
        data: rules,
        total: parseInt(totalCount[0]?.count?.toString() || '0', 10),
      };
    } catch (error) {
      throw error;
    }
  }
  // Update Point Rule
  async updatePointRule(
    id: string,
    data: PointRuleDto,
    tx = this.drizzle.db,
  ): Promise<PointRule[]> {
    try {
      const result = await tx
        .update(pointRulesSchema)
        .set(data)
        .where(eq(pointRulesSchema.id, id))
        .returning()
        .execute();

      if (!result.length) {
        throw new NotFoundException('Point rule not found');
      }

      return result;
    } catch (error) {
      throw error;
    }
  }
  // Delete Point Rule
  async removePointRule(
    id: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string }> {
    try {
      const result = await tx
        .delete(pointRulesSchema)
        .where(eq(pointRulesSchema.id, id))
        .execute();

      if (result.rowCount === 0) {
        throw new NotFoundException('Point rule not found');
      }

      return { message: 'Point rule deleted successfully' };
    } catch (error) {
      throw error;
    }
  }
  // Disable Point Rule
  async disablePointRule(
    id: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string }> {
    try {
      const result = await tx
        .update(pointRulesSchema)
        .set({ disabled: true })
        .where(eq(pointRulesSchema.id, id))
        .returning()
        .execute();

      if (!result.length) {
        throw new NotFoundException('Point rule not found');
      }

      return { message: 'Point rule disabled successfully' };
    } catch (error) {
      throw error;
    }
  }
  /**
   * Award points to student with critical safety measures implemented
   * @param pointModule
   * @param action
   * @param student_id
   * @returns
   */
  async awardPointsToStudent(
    pointModule: string,
    action: string,
    student_id: string,
  ) {
    // Critical Fix 1: Input validation to prevent invalid student IDs
    if (
      !student_id ||
      typeof student_id !== 'string' ||
      student_id.trim().length === 0
    ) {
      this.logger.warn(`Invalid student_id provided: ${student_id}`);
      return null;
    }

    // Critical Fix 2: Validate student exists in database
    const studentExists = await this.drizzle.db
      .select({ id: student_profiles.id })
      .from(student_profiles)
      .where(eq(student_profiles.id, student_id))
      .limit(1);

    if (!studentExists.length) {
      this.logger.warn(`Student not found: ${student_id}`);
      return null;
    }

    // Critical Fix 3: Distributed locking to prevent race conditions
    const lockKey = `lock:points:${this.sanitizeKey(student_id)}:${this.sanitizeKey(pointModule)}:${this.sanitizeKey(action)}`;
    const lockTimeout = 5000; // 5 seconds

    try {
      // Acquire distributed lock
      const locked = await this.redisService.client.set(
        lockKey,
        'locked',
        'PX',
        lockTimeout,
        'NX',
      );

      if (!locked) {
        this.logger.warn(
          `Failed to acquire lock for student ${student_id} points award. Concurrent operation in progress.`,
        );
        return null;
      }

      // Critical Fix 4: Database transaction for consistency
      return await this.drizzle.db.transaction(async (tx) => {
        return await this.processPointsAward(
          pointModule,
          action,
          student_id,
          tx,
        );
      });
    } catch (error) {
      this.logger.error(
        `Error in awardPointsToStudent for student ${student_id}:`,
        error,
      );
      throw error;
    } finally {
      // Always release the lock
      try {
        await this.redisService.client.del(lockKey);
      } catch (lockError) {
        this.logger.warn(
          `Failed to release lock for student ${student_id}:`,
          lockError,
        );
      }
    }
  }

  /**
   * Internal method to process points award within transaction
   * Critical Fix 5: Separated business logic for better error handling
   */
  private async processPointsAward(
    pointModule: string,
    action: string,
    student_id: string,
    tx: any,
  ) {
    // Fetch the point rule
    const pointRule = await this.getPointRule(pointModule, action, tx);
    if (!pointRule) {
      this.logger.warn(
        `Point rule not found for module: ${pointModule}, action: ${action}`,
      );
      return;
    }
    // Fetch the points configuration
    const pointsConfig = await this.getPointsConfigById(
      pointRule.points_config_id,
      tx,
    );

    if (!pointsConfig) {
      this.logger.warn(
        `Points configuration not found for id: ${pointRule.points_config_id}`,
      );
      return;
    }
    const { point_value, description } = pointsConfig[0];

    // Check frequency and determine if points should be awarded
    const shouldAwardPoints = await this.checkFrequency(
      pointRule.frequency,
      student_id,
      pointRule.id,
      tx,
    );

    if (!shouldAwardPoints) {
      return;
    }

    // Save the points log
    const result = await this.rewardPoints(
      {
        point_rule_id: pointRule.id,
        points: point_value,
        description,
        student_id: student_id,
      },
      tx,
    );

    // After successfully awarding points, update real-time leaderboard
    try {
      await this.updateRealtimeLeaderboard(
        student_id,
        point_value,
        pointModule,
        action,
      );
    } catch (error) {
      this.logger.warn('Failed to update real-time leaderboard', error);
    }

    // After successfully awarding points, invalidate leaderboard cache
    try {
      await this.invalidateLeaderboardCache(student_id);
    } catch (error) {
      this.logger.warn('Failed to invalidate leaderboard cache', error);
    }

    return result;
  }
  private async checkFrequency(
    frequency: string,
    studentId: string,
    pointRuleId: string,
    tx: any,
  ): Promise<boolean> {
    const now = new Date();
    let startDate: Date | null = null;

    switch (frequency) {
      case PointConstant.FREQUENCIES.ONCE_A_DAY:
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case PointConstant.FREQUENCIES.ONCE_A_WEEK:
        const firstDayOfWeek = now.getDate() - now.getDay();
        startDate = new Date(now.getFullYear(), now.getMonth(), firstDayOfWeek);
        break;
      case PointConstant.FREQUENCIES.ONCE_A_MONTH:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case PointConstant.FREQUENCIES.ONCE:
        startDate = new Date(0);
        break;
      case PointConstant.FREQUENCIES.EVERY_EVENT:
        return true;
      default:
        throw new Error('Invalid frequency');
    }

    if (startDate) {
      const existingLog = await tx
        .select()
        .from(pointsLogSchema)
        .where(
          and(
            eq(pointsLogSchema.student_id, studentId),
            eq(pointsLogSchema.point_rule_id, pointRuleId),
            gte(pointsLogSchema.created_at, startDate),
          ),
        )
        .execute();

      return existingLog.length === 0;
    }
    return true;
  }
  //Remove Point Logs
  async removePointsFromStudent(
    pointModule: string,
    action: string,
    studentId: string,
    tx = this.drizzle.db,
  ): Promise<void> {
    try {
      // Fetch the point rule
      const pointRule = await this.getPointRule(pointModule, action, tx);
      if (!pointRule) {
        throw new NotFoundException('Point rule not found');
      }

      // Fetch the existing point Rule
      const pointsConfig = await this.getPointsConfigById(
        pointRule.points_config_id,
        tx,
      );
      if (!pointsConfig) {
        throw new NotFoundException('Points configuration not found');
      }

      const { point_value, description } = pointsConfig[0];
      const negativePoints = point_value * -1;

      // Insert a new log entry with negative points
      await tx
        .insert(pointsLogSchema)
        .values({
          point_rule_id: pointRule.id,
          points: negativePoints,
          description: `Reversal of points: ${description}`,
          student_id: studentId,
        })
        .execute();

      this.logger.debug(`Removed points for action`);
    } catch (error: any) {
      this.logger.error(
        `Failed to remove points for studentId: ${studentId}, error: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Verifies if a student has received points for a specific module and action within the frequency period
   * @param student_id Student ID to check
   * @param pointModule Module name (e.g., User, Post, Quiz)
   * @param action Action name (e.g., Login, Like, Share)
   * @returns boolean indicating if points were already awarded within the frequency period
   */
  async verifyPointsAwarded(
    student_id: string,
    pointModule: string,
    action: string,
  ): Promise<boolean> {
    try {
      // Get the point rule for this module and action
      const pointRule = await this.getPointRule(
        pointModule,
        action,
        this.drizzle.db,
      );

      if (!pointRule) {
        return false;
      }

      const now = new Date();
      let startDate: Date | null = null;

      switch (pointRule.frequency) {
        case PointConstant.FREQUENCIES.ONCE_A_DAY:
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
          );
          break;
        case PointConstant.FREQUENCIES.ONCE_A_WEEK:
          const firstDayOfWeek = now.getDate() - now.getDay();
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            firstDayOfWeek,
          );
          break;
        case PointConstant.FREQUENCIES.ONCE_A_MONTH:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case PointConstant.FREQUENCIES.ONCE:
          startDate = new Date(0);
          break;
        case PointConstant.FREQUENCIES.EVERY_EVENT:
          return false;
        default:
          this.logger.warn(`Unknown frequency: ${pointRule.frequency}`);
          return false;
      }

      // Query for existing point logs
      const result = await this.drizzle.db
        .select()
        .from(pointsLogSchema)
        .where(
          and(
            eq(pointsLogSchema.student_id, student_id),
            eq(pointsLogSchema.point_rule_id, pointRule.id),
            gte(pointsLogSchema.created_at, startDate),
          ),
        )
        .execute();

      return result.length > 0;
    } catch (error) {
      this.logger.error(`Error verifying points awarded: ${error}`);
      return false;
    }
  }

  /**
   * Verifies if a student has received login points today
   * @param student_id Student ID to check
   * @returns boolean indicating if login points were awarded today
   */
  async verifyLoginPointsAwarded(student_id: string): Promise<boolean> {
    // No longer need to refresh materialized views on every login
    // Views are refreshed via background jobs

    return this.verifyPointsAwarded(
      student_id,
      PointConstant.MODULES.USER,
      PointConstant.ACTIONS.LOGIN,
    );
  }

  /**
   * Invalidates leaderboard cache for a student
   */
  private async invalidateLeaderboardCache(student_id: string): Promise<void> {
    try {
      // Get the user ID from the student profile
      const studentProfile =
        await this.drizzle.db.query.student_profiles.findFirst({
          where: eq(student_profiles.id, student_id),
          columns: {
            user_id: true,
          },
        });

      if (!studentProfile?.user_id) {
        return;
      }

      const userId = studentProfile.user_id;

      // Get all period types
      const periods = Object.values(PeriodEnumType);

      // Invalidate cache for each period
      for (const period of periods) {
        const viewName = this.getViewNameByPeriod(period);

        // Generate the pattern for all leaderboard caches for this view
        const leaderboardPattern = `${CACHE_PREFIXES.LEADERBOARD}:${viewName}:*`;

        // Generate the pattern for user rank caches
        const userRankPattern = `${CACHE_PREFIXES.LEADERBOARD}:${viewName}:user:${userId}`;

        // Invalidate both patterns
        await this.cacheService.invalidatePattern(leaderboardPattern);
        await this.cacheService.del(userRankPattern);
      }

      this.logger.debug(
        `Invalidated leaderboard cache for student ${student_id}`,
      );
    } catch (error) {
      this.logger.error(`Failed to invalidate leaderboard cache: ${error}`);
    }
  }

  /**
   * Gets the view name for a period type
   */
  private getViewNameByPeriod(period: PeriodEnumType): string {
    const viewNameMap: Record<PeriodEnumType, string> = {
      [PeriodEnumType.DAILY]: 'leaderboard_day',
      [PeriodEnumType.WEEKLY]: 'leaderboard_week',
      [PeriodEnumType.LAST_WEEK]: 'leaderboard_last_week',
      [PeriodEnumType.MONTHLY]: 'leaderboard_month',
      [PeriodEnumType.LAST_MONTH]: 'leaderboard_last_month',
      [PeriodEnumType.QUARTERLY]: 'leaderboard_current_quarter',
      [PeriodEnumType.CURRENT_QUARTER]: 'leaderboard_current_quarter',
      [PeriodEnumType.FIRST_QUARTER]: 'leaderboard_first_quarter',
      [PeriodEnumType.SECOND_QUARTER]: 'leaderboard_second_quarter',
      [PeriodEnumType.THIRD_QUARTER]: 'leaderboard_third_quarter',
      [PeriodEnumType.FOURTH_QUARTER]: 'leaderboard_fourth_quarter',
      [PeriodEnumType.YEARLY]: 'leaderboard_year',
      [PeriodEnumType.ALL_TIME]: 'leaderboard_all_time',
    };

    const viewName = viewNameMap[period];
    if (!viewName) {
      throw new Error(`Invalid period: ${period}`);
    }
    return viewName;
  }

  /**
   * Update real-time leaderboard when points are awarded
   */
  private async updateRealtimeLeaderboard(
    student_id: string,
    points: number,
    module: string,
    action: string,
  ): Promise<void> {
    try {
      // Queue a real-time leaderboard update job
      await this.generalQueue.add(
        'update-realtime-leaderboard',
        {
          student_id,
          points,
          module,
          action,
          timestamp: new Date(),
        },
        {
          delay: 100, // Small delay to ensure database consistency
          removeOnComplete: 50,
          removeOnFail: 10,
        },
      );

      this.logger.debug(
        `Queued real-time leaderboard update for student ${student_id} with ${points} points`,
      );
    } catch (error) {
      this.logger.error('Failed to queue real-time leaderboard update', error);
      throw error;
    }
  }

  /**
   * Sanitizes Redis keys to prevent injection attacks
   * Critical Fix 6: Safe Redis key generation
   */
  private sanitizeKey(key: string): string {
    return key.replace(/[^a-zA-Z0-9-_:]/g, '_');
  }
}
