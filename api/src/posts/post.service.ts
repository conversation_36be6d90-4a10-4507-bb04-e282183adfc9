import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { clubEventDto } from './dto/event.dto';
import { PostRepository } from '@/repositories/post.repository';
import {
  countries,
  institutions,
  student_club_memberships,
  student_clubs,
  student_profiles,
  StudentProfile,
  User,
  user_roles,
  users,
  post_engagement_types,
  postEngagementsSchema,
  PostEngagementType,
  UserRole,
} from '@/db/schema';
import {
  and,
  arrayOverlaps,
  asc,
  desc,
  eq,
  getTableColumns,
  ilike,
  inArray,
  isNull,
  or,
  SQL,
  sql,
  isNotNull,
} from 'drizzle-orm';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { parse, isBefore, isAfter, isEqual } from 'date-fns';
import { alias } from 'drizzle-orm/pg-core';
import {
  events,
  insertPostInput,
  opportunity,
  Post,
  posts,
  postCountries,
  postInstitutions,
  PostStatus,
  PostType,
  post_types,
  insertOpportunityInput,
  IEvent,
  post_statuses,
  postImages,
} from '@/db/schema/posts';
import { PostNotificationService } from './services/post-notification.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { postDto, eventDto, PostEngagementParamsDto } from './dto/post.dto';

import { PaginationParams } from '@/common/interfaces/pagination.interface';
import { PostServiceMessages } from '@app/shared/constants/post.constants';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { PointConstant } from '@app/shared/constants/points-system.constant';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';
import {
  generatePostKey,
  invalidatePostCaches,
} from './utils/unified-cache.utils';
import {
  DEFAULT_JOB_OPTIONS,
  QueueName,
  UploadJobType,
} from '@app/shared/queue/queue.constants';
import { UploadJobData } from '@app/shared/queue/queue.types';

@Injectable()
export class PostService {
  private readonly logger = new Logger(PostService.name);
  private readonly CACHE_PREFIX = CACHE_PREFIXES.POST;
  private readonly CACHE_TTL = CACHE_TTL.ONE_DAY;

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly postRepository: PostRepository,
    private readonly pointSystemRepository: PointSystemRepository,
    private readonly cacheService: CacheService,
    private readonly postNotificationService: PostNotificationService,
    @InjectQueue(QueueName.UPLOAD) private readonly uploadQueue: Queue,
  ) {}

  /**
   * Creates a new post.
   *
   * @param data - The post data.
   * @param user - The user creating the post.
   * @param imageUrl - The URL of the post image (optional).
   * @returns The created post data.
   */
  async createPost(
    data: postDto,
    user: User,
    attachments: Express.Multer.File[] = [],
  ) {
    let studentProfile: StudentProfile | undefined;

    // Extract notify_users but include it in the database insert
    // It's already properly converted to boolean by the DTO schema
    const { notify_users, ...sanitizedData } = data;

    const [createdPostData] = await this.postRepository.createPost({
      ...sanitizedData,
      status: attachments.length ? post_statuses.PENDING : data.status,
      postedBy: user.id,
      club_id:
        user.role === user_roles.STUDENT_ADMIN ? studentProfile?.club_id : null,
      isGlobal: data.isGlobal || false,
      notify_users: notify_users || false,
      scheduled_at: data?.scheduledAt || null,
    });

    if (createdPostData) {
      await this.uploadPostAttachments(
        createdPostData.id,
        attachments,
        data.status,
      );

      // Send notifications if needed
      if (data.notificationDeliverType || notify_users) {
        await this.sendPostNotifications(createdPostData, data);
      }
    }
    try {
      await this.cacheService.set(
        generatePostKey(
          this.cacheService,
          createdPostData!.id,
          this.CACHE_PREFIX,
        ),
        createdPostData,
        this.CACHE_TTL,
      );

      // Invalidate relevant caches
      await invalidatePostCaches(
        this.cacheService,
        createdPostData!.id,
        this.CACHE_PREFIX,
      );
    } catch (error) {
      this.logger.warn('Failed to update post cache', error);
    }

    return createdPostData;
  }

  /**
   * Creates a general post for a club.
   *
   * @param data - The post data.
   * @param user - The user creating the post.
   * @param imageUrl - The URL of the post image (optional).
   * @param clubId - The ID of the club.
   * @returns The created post data.
   * @throws {NotFoundException} If the club is not found or the student profile is not found.
   * @throws {UnauthorizedException} If the user is not authorized to create a post for the club.
   */
  async createClubGeneralPost(
    data: postDto,
    user: User,
    clubId: string,
    attachments: Express.Multer.File[] = [],
  ) {
    const club = await this.drizzle.db
      .select()
      .from(student_clubs)
      .where(eq(student_clubs.id, clubId))
      .then((data) => data[0]);

    if (!club) throw new NotFoundException(PostServiceMessages.ClubNotFound);

    // Extract notify_users but include it in the database insert

    const { notify_users, ...sanitizedData } = data;

    if (user.role === user_roles.STUDENT_ADMIN) {
      const studentProfile = user?.student_profile as StudentProfile;
      if (!studentProfile)
        throw new NotFoundException(PostServiceMessages.StudentProfileNotFound);

      if (club.club_admin !== user.id)
        throw new UnauthorizedException(
          PostServiceMessages.StudentNotAClubAdmin,
        );

      const createdPostData = await this.postRepository.createPost({
        ...sanitizedData,
        postedBy: user.id,
        status: attachments.length ? post_statuses.PENDING : data.status,
        club_id: clubId,
        notify_users: notify_users || false,
        scheduled_at: data?.scheduledAt || null,
      });

      if (createdPostData[0]) {
        await this.uploadPostAttachments(
          createdPostData[0].id,
          attachments,
          data.status,
        );

        // Send notifications if needed
        if (data.notificationDeliverType || notify_users) {
          await this.sendPostNotifications(createdPostData[0], data);
        }
      }

      try {
        await this.cacheService.set(
          generatePostKey(
            this.cacheService,
            createdPostData[0]!.id,
            this.CACHE_PREFIX,
          ),
          createdPostData,
          this.CACHE_TTL,
        );

        // Invalidate relevant caches
        await invalidatePostCaches(
          this.cacheService,
          createdPostData[0]!.id,
          this.CACHE_PREFIX,
        );
      } catch (error) {
        this.logger.warn('Failed to update post cache', error);
      }

      return createdPostData;
    }

    const [createdPostData] = await this.postRepository.createPost({
      ...sanitizedData,
      postedBy: user.id,
      club_id: clubId,
      status: attachments.length ? post_statuses.PENDING : data.status,
      notify_users: notify_users || false,
    });

    // Send notifications if needed
    if ((data.notificationDeliverType || notify_users) && createdPostData) {
      await this.sendPostNotifications(createdPostData, data);
      if (attachments.length) {
        await this.uploadPostAttachments(
          createdPostData.id,
          attachments,
          data.status,
        );
      }
    }

    try {
      await this.cacheService.set(
        generatePostKey(
          this.cacheService,
          createdPostData!.id,
          this.CACHE_PREFIX,
        ),
        createdPostData,
        this.CACHE_TTL,
      );

      // Invalidate relevant caches
      await invalidatePostCaches(
        this.cacheService,
        createdPostData!.id,
        this.CACHE_PREFIX,
      );
    } catch (error) {
      this.logger.warn('Failed to update post cache', error);
    }

    return createdPostData;
  }

  /**
   * Retrieves a list of posts based on the provided parameters.
   *
   * @param user - The user object.
   * @param paginationParams - The pagination parameters.
   * @param paginationParams.type - The post type.
   * @param paginationParams.status - The post status.
   * @param paginationParams.startDate - The start date for filtering posts.
   * @returns An object containing the list of posts and the total count.
   */
  async getPosts(
    user: User,
    {
      page,
      limit,
      sort,
      order,
      search,
      type,
      status,
      startDate,
      id,
    }: PaginationParams & {
      sort: keyof Post;
      type?: PostType;
      status?: PostStatus;
      startDate?: string;
      id?: string;
    },
  ) {
    // Don't use cache for filtered/paginated/search requests
    if (
      search ||
      type ||
      status ||
      startDate ||
      id ||
      page !== 1 ||
      limit !== 10
    ) {
      return this.getPostsFromDB(user, {
        page,
        limit,
        sort,
        order,
        search,
        type,
        status,
        startDate,
        id,
      });
    }

    // Generate cache key based on user role
    const cacheKey = this.cacheService.generateKey(
      ['all', user.role, user.id],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved posts from cache');
      return cachedData;
    }

    // If not in cache, get from database
    const posts = await this.getPostsFromDB(user, {
      page,
      limit,
      sort,
      order,
      search,
      type,
      status,
      startDate,
      id,
    });

    // Cache the results
    await this.cacheService.set(cacheKey, posts, this.CACHE_TTL);

    return posts;
  }

  private async getPostsFromDB(
    user: User,
    {
      page,
      limit,
      sort,
      order,
      search,
      type,
      status,
      startDate,
      id,
    }: PaginationParams & {
      sort: keyof Post;
      type?: PostType;
      status?: PostStatus;
      startDate?: string;
      id?: string;
    },
  ) {
    const where = [];
    if (id) {
      where.push(eq(posts.id, id));
    }
    if (search) {
      const searchPattern = `%${search.toString().toLocaleLowerCase()}%`;
      where.push(
        or(
          ilike(posts.title, searchPattern),
          ilike(posts.description, searchPattern),
        ),
      );
    }
    if (type) {
      where.push(eq(posts.type, type));
    }
    if (status) {
      where.push(eq(posts.status, status));
    }
    if (startDate) {
      where.push(this.getStartDateCondition(startDate));
    }
    if (
      user.role === user_roles.STUDENT ||
      user.role === user_roles.STUDENT_ADMIN
    ) {
      return this.getStudentPosts(
        user,
        {
          page,
          limit,
          sort,
          order,
        },
        where,
      );
    }
    if (user.role !== user_roles.SUPER_ADMIN) {
      where.push(or(eq(posts.postedBy, user.id), isNotNull(posts.club_id)));
    }

    const postResults = await this.drizzle.db.query.posts.findMany({
      where: and(...where),
      with: {
        postedBy: {
          with: {
            profile: {
              columns: {
                id: true,
                email: true,
                name: true,
              },
            },
            student_profile: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
        countries: true,
        institutions: true,
        opportunity: true,
        event: true,
        postEngagements: true,
        images: true,
      },
      orderBy: order === 'asc' ? asc(posts[sort]) : desc(posts[sort]),
      limit,
      offset: (page - 1) * limit,
    });

    const total = await this.drizzle.db.$count(posts, and(...where));

    return {
      data: postResults,
      total,
    };
  }

  /**
   * Retrieves club posts based on the provided parameters.
   *
   * @param user - The user object.
   * @param paginationParams - The pagination parameters.
   * @param clubId - The ID of the club.
   * @returns An object containing the post results and the total count.
   * @throws NotFoundException if the user is not a member of the club.
   */
  async getClubPosts(
    user: User,
    {
      page = 1,
      limit = 10,
      sort,
      order,
      type,
    }: PaginationParams & {
      sort: keyof Post;
      type?: PostType;
      status?: PostStatus;
      startDate?: string;
    },
    clubId: string,
  ) {
    // Don't use cache for filtered/paginated requests
    if (type || page !== 1 || limit !== 10) {
      return this.getClubPostsFromDB(
        user,
        { page, limit, sort, order, type, search: '' },
        clubId,
      );
    }

    const cacheKey = this.cacheService.generateKey(
      ['club-posts', clubId, user.role],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved club posts from cache');
      return cachedData;
    }

    const posts = await this.getClubPostsFromDB(
      user,
      { page, limit, sort, order, type, search: '' },
      clubId,
    );

    // Cache the results
    await this.cacheService.set(cacheKey, posts, this.CACHE_TTL);

    return posts;
  }

  private async getClubPostsFromDB(
    user: User,
    {
      page,
      limit,
      sort,
      order,
      type,
    }: PaginationParams & {
      sort: keyof Post;
      type?: PostType;
      status?: PostStatus;
      startDate?: string;
    },
    clubId: string,
  ) {
    const where = [];

    if (type) {
      where.push(eq(posts.type, type));
    }

    if (
      user.role == user_roles.STUDENT ||
      user.role == user_roles.STUDENT_ADMIN
    ) {
      const studentClubs = await this.drizzle.db.query.student_profiles
        .findFirst({
          where: eq(student_profiles.user_id, user.id),
          with: { club_memberships: true },
        })
        .then((data) =>
          data ? data.club_memberships.map((club) => club.club_id) : [],
        );

      if (!studentClubs.length || !studentClubs.includes(clubId))
        throw new NotFoundException(PostServiceMessages.StudentNotAClubMember);
      where.push(eq(posts.status, 'active'));
    }

    const postResults = await this.drizzle.db.query.posts.findMany({
      where: and(...where, eq(posts.club_id, clubId)),
      with: {
        postedBy: {
          with: {
            profile: true,
          },
        },
        countries: {
          with: {
            country: true,
          },
        },
        institutions: {
          with: {
            institution: true,
          },
        },
        opportunity: true,
        event: true,
        postEngagements: true,
        images: true,
      },
      orderBy: order === 'asc' ? asc(posts[sort]) : desc(posts[sort]),
      limit,
      offset: (page - 1) * limit,
    });

    const total = await this.drizzle.db.$count(
      posts,
      and(...where, eq(posts.club_id, clubId)),
    );

    return {
      data: postResults,
      total,
    };
  }

  /**
   * Retrieves student posts based on the provided parameters.
   *
   * @param userId - The ID of the user.
   * @param options - The pagination, sorting, and ordering options.
   * @param filters - Optional filters for the query.
   * @returns A Promise that resolves to an array of student posts.
   * @throws NotFoundException if the student profile is not found.
   */
  async getStudentPosts(
    user: User,
    {
      page = 1,
      limit = 10,
      sort,
      order,
    }: Omit<PaginationParams, 'search'> & {
      sort: keyof Post;
    },
    filters?: (SQL<unknown> | undefined)[],
  ) {
    // Don't use cache for filtered/paginated requests
    if (filters?.length || page !== 1 || limit !== 10) {
      return this.getStudentPostsFromDB(
        user,
        { page, limit, sort, order },
        filters,
      );
    }

    // Generate cache key based on student ID and role
    const cacheKey = this.cacheService.generateKey(
      ['student-posts', user.student_profile?.id ?? 'no-profile', user.role],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved student posts from cache');
      return cachedData;
    }

    // If not in cache, get from database
    const posts = await this.getStudentPostsFromDB(
      user,
      { page, limit, sort, order },
      filters,
    );

    // Cache the results
    await this.cacheService.set(cacheKey, posts, this.CACHE_TTL);

    return posts;
  }

  private async getStudentPostsFromDB(
    user: User,
    {
      page,
      limit,
      sort,
      order,
    }: Omit<PaginationParams, 'search'> & {
      sort: keyof Post;
    },
    filters?: (SQL<unknown> | undefined)[],
  ) {
    if (!user.student_profile)
      throw new NotFoundException(PostServiceMessages.StudentProfileNotFound);
    const student = user.student_profile as StudentProfile;

    let studentLevel = 1;
    if (student.graduation_date && student.enrollment_date) {
      if (student.graduation_date < new Date().getFullYear()) {
        studentLevel = 4;
      } else {
        studentLevel = new Date().getFullYear() - student.enrollment_date;
      }
    }

    const where: (SQL<unknown> | undefined)[] = filters ?? [];
    const studentClubs = await this.drizzle.db.query.student_club_memberships
      .findMany({
        where: eq(student_club_memberships.student_id, student.id),
      })
      .then((data) => data.map((club) => club.club_id));

    where.push(
      or(
        and(
          isNull(postInstitutions.institutionId),
          isNull(postCountries.countryId),
        ),
        or(
          eq(posts.isGlobal, true),
          and(
            eq(postCountries.countryId, student.country_id),
            eq(postInstitutions.institutionId, student.institution_id),
          ),
        ),
      ),
      or(
        isNull(opportunity.id),
        or(
          arrayOverlaps(opportunity.eligibility, [studentLevel]),
          eq(opportunity.eligibility, []),
        ),
      ),
    );

    if (studentClubs.length) {
      where.push(
        or(inArray(posts.club_id, studentClubs), isNull(posts.club_id)),
      );
    } else {
      where.push(isNull(posts.club_id));
    }

    const postedBy = alias(users, 'postedBy');
    const allPosts = this.drizzle.db
      .select({
        ...getTableColumns(posts),
        postedBy: {
          id: postedBy.id,
          first_name: student_profiles.first_name,
          last_name: student_profiles.last_name,
          profile_pic_url: postedBy.profile_pic_url,
          email: postedBy.email,
        },
        institutions: sql`COALESCE(jsonb_agg(DISTINCT jsonb_build_object(
        'id', ${institutions.id},
        'name', ${institutions.name},
        'location', ${institutions.location}
      )) FILTER (WHERE ${institutions.id} IS NOT NULL), '[]'::jsonb)`.as(
          'institutions',
        ),
        countries: sql`COALESCE(jsonb_agg(DISTINCT jsonb_build_object(
        'id', ${countries.id},
        'name', ${countries.name}
      )) FILTER (WHERE ${countries.id} IS NOT NULL), '[]'::jsonb)`.as(
          'countries',
        ),
        opportunity: getTableColumns(opportunity),
        event: getTableColumns(events),
        likes: this.drizzle.db.$count(
          postEngagementsSchema,
          and(eq(postEngagementsSchema.postId, posts.id)),
        ),
        userLiked: sql`EXISTS (
        SELECT 1
        FROM ${postEngagementsSchema}
        WHERE ${postEngagementsSchema.postId} = ${posts.id}
          AND ${postEngagementsSchema.student_profile_id} = ${student.id}
          AND ${postEngagementsSchema.post_engagement_type} = ${post_engagement_types.like}
      )::boolean`.as('userLiked'),
        images: sql`COALESCE(jsonb_agg(DISTINCT jsonb_build_object(
        'id', ${postImages.id},
        'imageUrl', ${postImages.imageUrl}
      )) FILTER (WHERE ${postImages.id} IS NOT NULL), '[]'::jsonb)`.as(
          'images',
        ),
      })
      .from(posts)
      .leftJoin(postedBy, eq(posts.postedBy, postedBy.id))
      .leftJoin(student_profiles, eq(postedBy.id, student_profiles.user_id))
      .leftJoin(opportunity, eq(posts.id, opportunity.postId))
      .leftJoin(events, eq(posts.id, events.postId))
      .leftJoin(postInstitutions, eq(posts.id, postInstitutions.postId))
      .leftJoin(
        institutions,
        eq(postInstitutions.institutionId, institutions.id),
      )
      .leftJoin(postCountries, eq(posts.id, postCountries.postId))
      .leftJoin(countries, eq(postCountries.countryId, countries.id))
      .leftJoin(postImages, eq(posts.id, postImages.postId))
      .where(
        and(...where, eq(posts.status, 'active'), eq(posts.disabled, false)),
      )
      .groupBy(
        posts.id,
        postedBy.id,
        student_profiles.id,
        opportunity.id,
        events.id,
      );

    const data = await allPosts
      .orderBy(order === 'asc' ? asc(posts[sort]) : desc(posts[sort]), posts.id)
      .limit(limit)
      .offset((page - 1) * limit)
      .then((data) =>
        data.map((post) => ({
          ...post,
          isLive: this.isPostLive(post as never),
        })),
      );

    const total = (await allPosts).length;
    return {
      data,
      total,
    };
  }

  /**
   * Retrieves post engagements based on the provided parameters.
   *
   * @param page - The page number for pagination.
   * @param limit - The number of items per page.
   * @param type - The type of post engagement.
   * @returns An object containing the post engagements and the total count.
   *
   * */

  async getPostEngagements(
    postId: string,
    {
      page,
      limit,
      type,
    }: PostEngagementParamsDto & {
      type: PostEngagementType;
    },
  ) {
    const postEngagements =
      await this.drizzle.db.query.postEngagementsSchema.findMany({
        where: and(
          eq(postEngagementsSchema.postId, postId),
          eq(postEngagementsSchema.post_engagement_type, type),
        ),
        with: {
          student_profile: {
            with: {
              user: true,
            },
          },
          post: true,
        },
        limit,
        offset: (page - 1) * limit,
      });

    const total = await this.drizzle.db.$count(
      postEngagementsSchema,
      and(
        eq(postEngagementsSchema.postId, postId),
        eq(postEngagementsSchema.post_engagement_type, type),
      ),
    );

    return {
      data: postEngagements,
      total,
    };
  }

  /**
   * Get trending post by ID (cached for 30 minutes)
   * Trending posts are posts with high engagement or marked as trending
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.TRENDING,
    ttl: CACHE_TTL.THIRTY_MINUTES,
    keyGenerator: (args) => [args[0]],
    condition: (args) => !!args[0],
  })
  async getTrendingById(id: string) {
    this.logger.debug(`Fetching trending post ${id} from database (not cache)`);

    const post = await this.drizzle.db.query.posts.findFirst({
      where: eq(posts.id, id),
      with: {
        postedBy: {
          with: {
            profile: {
              columns: {
                id: true,
                email: true,
                name: true,
              },
            },
            student_profile: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
        countries: {
          with: {
            country: true,
          },
        },
        institutions: {
          with: {
            institution: true,
          },
        },
        opportunity: true,
        event: true,
        postEngagements: true,
        images: true,
        club: true,
      },
    });

    if (!post) {
      throw new NotFoundException(`Trending post with ID ${id} not found`);
    }

    // Add engagement metrics for trending analysis
    const engagementCount = post.postEngagements?.length || 0;
    const likeCount =
      post.postEngagements?.filter(
        (engagement) =>
          engagement.post_engagement_type === post_engagement_types.like,
      ).length || 0;
    const shareCount =
      post.postEngagements?.filter(
        (engagement) =>
          engagement.post_engagement_type === post_engagement_types.share,
      ).length || 0;

    return {
      ...post,
      engagementMetrics: {
        totalEngagements: engagementCount,
        likes: likeCount,
        shares: shareCount,
        trendingScore: likeCount * 1 + shareCount * 2,
      },
    };
  }

  /**
   * Deletes a post with the specified postId.
   *
   * @param {string} postId - The ID of the post to delete.
   * @param {User} user - The user performing the delete operation.
   * @returns {Promise<void>} - A promise that resolves when the post is deleted successfully.
   * @throws {NotFoundException} - If the post with the specified postId is not found.
   * @throws {UnauthorizedException} - If the user is not authorized to delete the post.
   */
  async deletePost(postId: string, user: User): Promise<void> {
    const [existingPost] = await this.drizzle.db
      .select()
      .from(posts)
      .where(eq(posts.id, postId));

    if (!existingPost)
      throw new NotFoundException(PostServiceMessages.PostNotFound);

    if (
      existingPost.postedBy !== user.id &&
      user.role === user_roles.STUDENT_ADMIN
    )
      throw new UnauthorizedException(PostServiceMessages.UnAuthorized);

    await this.drizzle.db.delete(posts).where(eq(posts.id, postId));

    try {
      // Invalidate all related post caches
      await invalidatePostCaches(this.cacheService, postId, this.CACHE_PREFIX);
    } catch (error) {
      this.logger.warn('Failed to invalidate post caches', error);
    }
  }

  async bulkDeletePosts(postIds: string[]) {
    // Fetch posts and their engagement counts to prevent deletion if engagements exist.
    const postsWithEngagements = await this.drizzle.db
      .select({
        id: posts.id,
        engagementCount:
          sql<number>`count(${postEngagementsSchema.id})`.mapWith(Number),
      })
      .from(posts)
      .leftJoin(
        postEngagementsSchema,
        eq(posts.id, postEngagementsSchema.postId),
      )
      .where(inArray(posts.id, postIds))
      .groupBy(posts.id);

    const postsWithActiveEngagements = postsWithEngagements.filter(
      (p) => p.engagementCount > 0,
    );

    if (postsWithActiveEngagements.length > 0) {
      const postIdsWithEngagements = postsWithActiveEngagements.map(
        (p) => p.id,
      );
      throw new BadRequestException(
        `Cannot delete posts with existing engagements. Posts with engagements: ${postIdsWithEngagements.join(', ')}`,
      );
    }

    // Proceed with hard deletion if no posts have engagements
    const successfullyDeletedPostIds: string[] = [];
    await this.drizzle.db.transaction(async (tx) => {
      for (const postId of postIds) {
        // Check if post exists before attempting to delete.
        // This is a safeguard, though the inArray above should mean only existing post IDs are processed.
        const [existingPost] = await tx
          .select({ id: posts.id })
          .from(posts)
          .where(eq(posts.id, postId));

        if (!existingPost) {
          this.logger.warn(
            `Post with ID ${postId} not found during bulk hard delete operation.`,
          );
          continue; // Skip if post doesn't exist
        }

        // Perform hard delete
        await tx.delete(posts).where(eq(posts.id, postId));
        successfullyDeletedPostIds.push(postId);
      }
    });

    try {
      // Invalidate caches for successfully hard-deleted posts
      for (const postId of successfullyDeletedPostIds) {
        await invalidatePostCaches(
          this.cacheService,
          postId,
          this.CACHE_PREFIX,
        );
      }
    } catch (error) {
      this.logger.error(
        'Failed to invalidate post caches during bulk hard delete operation',
        error,
      );
    }
  }

  /**
   * Updates a post with the provided data.
   *
   * @param data - The partial data to update the post with.
   * @param id - The ID of the post to update.
   * @param user - The user performing the update.
   * @param image - The image file to update the post with.
   * @throws {NotFoundException} If the post with the provided ID is not found.
   * @throws {UnauthorizedException} If the user is not authorized to update the post.
   */
  async updatePost(
    data: Partial<insertPostInput>,
    id: string,
    user: User,
    attachments: Express.Multer.File[] = [],
  ) {
    const [existingPost] = await this.drizzle.db
      .select()
      .from(posts)
      .where(eq(posts.id, id));

    if (!existingPost)
      throw new NotFoundException(PostServiceMessages.PostNotFound);
    if (
      user.role === user_roles.STUDENT_ADMIN &&
      existingPost.postedBy !== user.id
    )
      throw new UnauthorizedException(PostServiceMessages.UnAuthorized);

    if (
      existingPost.postedBy !== user.id &&
      user.role === user_roles.STUDENT_ADMIN
    )
      throw new UnauthorizedException(PostServiceMessages.UnAuthorized);

    const updatedPost = await this.drizzle.db.transaction(async (tx) => {
      const [updatedPost] = await this.postRepository.updatePost(
        {
          ...data,
          status: attachments.length ? post_statuses.PENDING : data.status,
        },
        id,
        tx,
      );
      if (!updatedPost)
        throw new InternalServerErrorException(
          'Failed to update post, try again',
        );
      if (attachments.length) {
        await tx
          .delete(postImages)
          .where(eq(postImages.postId, updatedPost?.id));
      }
      return updatedPost;
    });

    try {
      // Invalidate all related post caches
      await invalidatePostCaches(this.cacheService, id, this.CACHE_PREFIX);

      // Update the specific post cache with new data
      if (updatedPost) {
        await this.uploadPostAttachments(
          updatedPost.id,
          attachments,
          data.status || post_statuses.PENDING,
        );
        return await this.getPostById(id, user);
      }
    } catch (error) {
      this.logger.warn('Failed to update post caches', error);
    }

    return updatedPost;
  }

  /**
   * Likes a post with the specified postId.
   * @param postId  The ID of the post to like
   * @param userId  The ID of the user liking the post
   * @returns
   */
  async likePost(postId: string, user: User) {
    const post = await this.drizzle.db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        postEngagements: {
          where: (engagement) =>
            eq(engagement.post_engagement_type, post_engagement_types.like),
        },
      },
    });

    if (!post) throw new NotFoundException(PostServiceMessages.PostNotFound);
    const existingLike = post.postEngagements?.find(
      (postEngagements) =>
        postEngagements.student_profile_id === user!.student_profile!.id,
    );

    let result;
    if (existingLike) {
      const deleteResult = await this.drizzle.db
        .delete(postEngagementsSchema)
        .where(eq(postEngagementsSchema.id, existingLike.id))
        .execute();

      if (deleteResult.rowCount && deleteResult.rowCount > 0) {
        if (user?.student_profile?.id) {
          await this.pointSystemRepository.removePointsFromStudent(
            PointConstant.MODULES.POST,
            PointConstant.ACTIONS.UNLIKE_POST,
            user.student_profile.id,
          );
        }
      } else {
        throw new Error('Failed to remove points for un-liking');
      }
      result = deleteResult;
    } else {
      if (user?.student_profile?.id) {
        const pointsAlreadyAwarded =
          await this.pointSystemRepository.verifyPointsAwarded(
            user.student_profile.id,
            PointConstant.MODULES.POST,
            PointConstant.ACTIONS.LIKE_POST,
          );
        if (!pointsAlreadyAwarded) {
          await this.pointSystemRepository.awardPointsToStudent(
            PointConstant.MODULES.POST,
            PointConstant.ACTIONS.LIKE_POST,
            user.student_profile.id,
          );
        }
      }
      result = await this.drizzle.db.insert(postEngagementsSchema).values({
        postId,
        student_profile_id: user!.student_profile!.id,
        post_engagement_type: post_engagement_types.like,
      });
    }

    try {
      await invalidatePostCaches(this.cacheService, postId, this.CACHE_PREFIX);

      const updatedPost = await this.drizzle.db.query.posts.findFirst({
        where: eq(posts.id, postId),
        with: {
          postEngagements: true,
        },
      });

      if (updatedPost) {
        await this.cacheService.set(
          generatePostKey(this.cacheService, postId, this.CACHE_PREFIX),
          updatedPost,
          this.CACHE_TTL,
        );
      }
    } catch (error) {
      this.logger.warn('Failed to update post caches after like/unlike', error);
    }

    return result;
  }

  /**
   *  Shares a post with the specified postId.
   * @param postId  The ID of the post to share
   * @param userId  The ID of the user sharing the post
   */

  async sharePost(postId: string, user: User) {
    const post = await this.drizzle.db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        postEngagements: {
          where: (engagement) =>
            eq(engagement.post_engagement_type, post_engagement_types.share),
        },
      },
    });

    if (!post) throw new NotFoundException(PostServiceMessages.PostNotFound);
    const existingShare = post.postEngagements?.find(
      (postEngagements) =>
        postEngagements.student_profile_id === user!.student_profile!.id,
    );

    if (existingShare) {
      return;
    }

    //Award points to the student for sharing the post
    if (user?.student_profile?.id) {
      const pointsAlreadyAwarded =
        await this.pointSystemRepository.verifyPointsAwarded(
          user.student_profile.id,
          PointConstant.MODULES.POST,
          PointConstant.ACTIONS.SHARE_POST,
        );

      if (!pointsAlreadyAwarded) {
        await this.pointSystemRepository.awardPointsToStudent(
          PointConstant.MODULES.POST,
          PointConstant.ACTIONS.SHARE_POST,
          user.student_profile.id,
        );
      }
    }

    const result = await this.drizzle.db.insert(postEngagementsSchema).values({
      postId,
      student_profile_id: user!.student_profile!.id,
      post_engagement_type: post_engagement_types.share,
    });

    try {
      // Invalidate all related post caches
      await invalidatePostCaches(this.cacheService, postId, this.CACHE_PREFIX);

      // Update the specific post cache
      const updatedPost = await this.drizzle.db.query.posts.findFirst({
        where: eq(posts.id, postId),
        with: {
          postEngagements: true,
        },
      });

      if (updatedPost) {
        await this.cacheService.set(
          generatePostKey(this.cacheService, postId, this.CACHE_PREFIX),
          updatedPost,
          this.CACHE_TTL,
        );
      }
    } catch (error) {
      this.logger.warn('Failed to update post caches after share', error);
    }

    return result;
  }

  /**
   * Returns the SQL condition based on the provided start date.
   * @param startDate - The start date to filter the SQL condition.
   * @returns The SQL condition based on the start date.
   */
  private getStartDateCondition(startDate: string): SQL<unknown> | undefined {
    switch (startDate) {
      case 'ongoing':
        return or(
          and(
            sql`${events.startDate} <= CURRENT_DATE`,
            sql`${events.endDate} >= CURRENT_DATE`,
          ),
          and(
            sql`${opportunity.startDate} <= CURRENT_DATE`,
            sql`${opportunity.endDate} >= CURRENT_DATE`,
          ),
        );
      case 'upcoming':
        return or(
          sql`DATE(${events.startDate}) > CURRENT_DATE`,
          sql`DATE(${opportunity.startDate}) > CURRENT_DATE`,
        );
      case 'past':
        return or(
          and(
            sql`DATE(${events.startDate}) < CURRENT_DATE`,
            sql`DATE(${events.endDate}) < CURRENT_DATE`,
          ),
          sql`DATE(${opportunity.startDate}) < CURRENT_DATE`,
          sql`DATE(${opportunity.endDate}) < CURRENT_DATE`,
        );
      default:
        return or(
          sql`DATE(${events.startDate}) = ${startDate}`,
          sql`DATE(${opportunity.startDate}) = ${startDate}`,
        );
    }
  }

  /**
   * Checks if the given country IDs exist in the database.
   * Throws a BadRequestException if any of the country IDs don't exist.
   *
   * @param countriesData - An array of country IDs to check.
   * @returns Promise<void>
   * @throws BadRequestException - If any of the country IDs don't exist.
   */
  async checkNonExistentCountries(countriesData: string[]) {
    const existingCountries = await this.drizzle.db
      .select({ id: countries.id })
      .from(countries)
      .where(inArray(countries.id, countriesData))
      .then((data) => data.map((country) => country.id));

    const nonExistentCountries = countriesData.filter(
      (id) => !existingCountries.includes(id),
    );
    if (nonExistentCountries.length)
      throw new BadRequestException(
        `Some country Ids don't exist: ${nonExistentCountries}`,
      );
  }

  /**
   * Checks if the given institution IDs exist in the database.
   * Throws a BadRequestException if any of the institution IDs don't exist.
   *
   * @param institutionData - An array of institution IDs to check.
   * @returns Promise<void>
   * @throws BadRequestException - If any of the institution IDs don't exist.
   */
  async checkNonExistentInstitutions(institutionData: string[]) {
    const existingInstitutions = await this.drizzle.db
      .select({ id: institutions.id })
      .from(institutions)
      .where(inArray(institutions.id, institutionData))
      .then((data) => data.map((institution) => institution.id));

    const nonExistentInstitutions = institutionData.filter(
      (id) => !existingInstitutions.includes(id),
    );
    if (nonExistentInstitutions.length)
      throw new BadRequestException(
        `Some institution Ids don't exist: ${nonExistentInstitutions}`,
      );
  }

  private isPostLive(post: IsPostLiveInput) {
    const now = new Date();

    const parseFullDateTime = (dateTimeStr: string) =>
      parse(dateTimeStr, 'yyyy-MM-dd HH:mm:ss.SSSSSS', new Date());

    if (post.type === post_types.EVENT) {
      const start = parseFullDateTime(post.event.startDate);
      const end = parseFullDateTime(post.event.endDate);

      return isBefore(start, now) && (isAfter(end, now) || isEqual(end, now));
    }

    if (post.type === post_types.OPPORTUNITY) {
      const start = parseFullDateTime(post.opportunity.startDate);
      const end = parseFullDateTime(post.opportunity.endDate);

      return isBefore(start, now) && (isAfter(end, now) || isEqual(end, now));
    }

    return false;
  }

  async addUploadJob(data: UploadJobData, jobId?: string): Promise<string> {
    try {
      const job = await this.uploadQueue.add(UploadJobType.UPLOAD_FILE, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
      });
      this.logger.debug(`Added upload job ${job.id}`);
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(`Failed to add upload job: ${err.message}`, err.stack);
      throw error;
    }
  }

  async uploadPostAttachments(
    postId: string,
    attachments: Express.Multer.File[],
    status: PostStatus,
  ): Promise<void> {
    for (let i = 0; i < attachments.length; i++) {
      const attachment = attachments[i];

      if (!attachment) continue;

      const isLastImage = i === attachments.length - 1;

      await this.addUploadJob({
        file: attachment,
        postId,
        isLastImage,
        status,
      });
    }
  }

  /**
   * Helper method to prepare and send notifications for a post
   * This consolidates the notification logic across different post creation methods
   *
   * @param postData - The post data
   * @param postDto - The original post DTO with notification settings
   * @returns Promise<void>
   */
  public async sendPostNotifications(
    postData: Post,
    postDto: postDto | eventDto | clubEventDto,
  ): Promise<void> {
    // Skip if neither notify_users nor notifications are provided
    if (!postDto.notify_users && !postDto.notificationRecipients) {
      this.logger.log(
        `No notification settings provided for post ${postData.id}, skipping notifications`,
      );
      return;
    }

    // If we reach here, notifications are intended.
    // Therefore, notifications should only go to active users.

    // notify_users is now properly converted to boolean by the DTO schema
    const notifyUsers = !!postDto.notify_users;

    // Determine notification channels - always include email for post notifications
    // Default to both push and email channels for better delivery
    let channels: string[] = ['push', 'email'];
    if (postDto.notificationDeliverType) {
      channels =
        postDto.notificationDeliverType[0] === 'all'
          ? ['push', 'email']
          : postDto.notificationDeliverType;
    }
    // Ensure email is always included for post notifications
    if (!channels.includes('email')) {
      channels.push('email');
      this.logger.log(
        `Added email channel to ensure email delivery for post ${postData.id}`,
      );
    }
    this.logger.log(
      `Notification channels for post ${postData.id}: ${channels.join(', ')}`,
    );

    // Determine recipients
    let recipients: string[] = [];
    if (notifyUsers) {
      recipients = ['student', 'student_admin'];
      this.logger.log(
        `Preparing notifications for post ${postData.id} with notify_users=true (active users only)`,
      );
    }
    // If notify_users is false but notifications object has recipients, use those
    else if (postDto.notificationRecipients) {
      recipients =
        postDto.notificationRecipients[0] === 'all'
          ? []
          : postDto.notificationRecipients;
      this.logger.log(
        `Preparing notifications for post ${postData.id} with custom recipients: ${recipients.join(
          ', ',
        )} (active users only)`,
      );
    }

    // Send notification
    try {
      await this.postNotificationService.sendPostNotification({
        postId: postData.id,
        title: postData.title,
        description: postData.description,
        postType: postData.type || 'general',
        isGlobal: postData.isGlobal ?? true,
        clubId: postData.club_id || undefined,
        channels: channels as ('push' | 'email' | 'in_app')[],
        recipients: recipients as UserRole[],
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to queue notification for post ${postData.id}: ${error?.message}`,
        error?.stack,
      );
    }
  }

  /**
   * Retrieves a single post by its ID with caching support
   *
   * @param id - The post ID
   * @param user - The user requesting the post (for role-based access)
   * @returns The post data or throws NotFoundException
   */
  async getPostById(id: string, user: User): Promise<Post> {
    const cacheKey = generatePostKey(this.cacheService, id, this.CACHE_PREFIX);

    const cachedPost = await this.cacheService.get(cacheKey);
    if (cachedPost) {
      this.logger.debug(`Retrieved post ${id} from cache`);
      return cachedPost;
    }

    const post = await this.drizzle.db.query.posts.findFirst({
      where: eq(posts.id, id),
      with: {
        postedBy: {
          with: {
            profile: {
              columns: {
                id: true,
                email: true,
                name: true,
              },
            },
            student_profile: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
        countries: {
          with: {
            country: true,
          },
        },
        institutions: {
          with: {
            institution: true,
          },
        },
        opportunity: true,
        event: true,
        postEngagements: true,
        images: true,
        club: true,
      },
    });

    if (!post) {
      throw new NotFoundException(PostServiceMessages.PostNotFound);
    }

    // Check if user has access to this post based on role and post visibility
    if (
      user.role === user_roles.STUDENT ||
      user.role === user_roles.STUDENT_ADMIN
    ) {
      // Students can only see active posts
      if (post.status !== post_statuses.ACTIVE) {
        throw new NotFoundException(PostServiceMessages.PostNotFound);
      }

      // For club-specific posts, check if user is a member
      if (post.club_id) {
        const studentProfile = user.student_profile as StudentProfile;
        if (!studentProfile) {
          throw new NotFoundException(
            PostServiceMessages.StudentProfileNotFound,
          );
        }

        const studentClubs =
          await this.drizzle.db.query.student_club_memberships
            .findMany({
              where: eq(student_club_memberships.student_id, studentProfile.id),
            })
            .then((data) => data.map((club) => club.club_id));

        if (!studentClubs.includes(post.club_id)) {
          throw new NotFoundException(
            PostServiceMessages.StudentNotAClubMember,
          );
        }
      }
    }

    // Cache the result
    try {
      await this.cacheService.set(cacheKey, post, this.CACHE_TTL);
      this.logger.debug(`Cached post ${id}`);
    } catch (error) {
      this.logger.warn(`Failed to cache post ${id}`, error);
    }

    return post;
  }
}

interface IsPostLiveInput extends Post {
  event: IEvent & {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
  };
  opportunity: insertOpportunityInput & {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
  };
}
