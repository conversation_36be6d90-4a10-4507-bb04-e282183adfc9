import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { QuizRepository } from '../repository/quiz.repository';
import { NotificationService } from '@app/shared/notification/notification.service';
import { EnhancedNotificationService } from '@app/shared/enhanced-notification/enhanced-notification.service';
import { notification_types } from '@/db/schema/notification_system';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { eq } from 'drizzle-orm';

@Injectable()
export class QuizScheduler {
  constructor(
    private quizRepository: QuizRepository,
    private notificationService: NotificationService,
    private enhancedNotificationService: EnhancedNotificationService,
    private drizzle: DrizzleService,
  ) {}
  private readonly logger = new Logger(QuizScheduler.name);

  @Cron(CronExpression.EVERY_10_MINUTES)
  async handleScheduledQuizzes() {
    const activatedQuizzes = await this.quizRepository.checkAndActivateQuiz();

    // Send notification using both the old and new notification services
    // Old service for backward compatibility
    await this.notificationService.sendTopicNotification({
      topic: 'new-quizzes',
      title: 'New quizzes are available',
      body: 'New quizzes are available for you to attempt',
    });

    // New enhanced notification service
    try {
      // Get notification type for active quiz
      // Get notification type for active quiz
      const notificationTypes = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(eq(notification_types.code, 'active_quiz'));

      const quizNotificationType = notificationTypes[0];

      if (quizNotificationType && activatedQuizzes?.result?.length > 0) {
        await this.enhancedNotificationService.sendNotificationToUsers({
          notificationTypeId: quizNotificationType.id,
          data: { quizzes: activatedQuizzes.result },
          targetAudience: { roles: ['student', 'student_admin'] },
          channels: ['push', 'in_app'],
        });
      }
    } catch (error) {
      this.logger.error(
        'Failed to send enhanced notification for activated quizzes',
        error instanceof Error ? error.stack : String(error),
      );
      // Continue execution even if enhanced notification fails
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleQuizCompletion() {
    await this.quizRepository.checkAndCompleteQuiz();
  }
}
