import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtHelperModule } from 'src/jwt-helper/jwt-helper.module';
import { EmailModule } from 'src/mail/email.module';
import { ScheduleModule } from '@nestjs/schedule';

import { PointSystemModule } from '@/point-system/point_system.module';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import { NotificationModule } from '@app/shared/notification/notification.module';
import { McqModule } from '@/mcq/mcq.module';

@Module({
  imports: [
    JwtHelperModule,
    EmailModule,
    ScheduleModule.forRoot(),
    NotificationModule,
    McqModule,
    PointSystemModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, StudentProfileService],
  exports: [AuthService],
})
export class AuthModule {}
